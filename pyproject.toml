[project]
name = "logmodel"
version = "0.1.0"
description = "应用服务报错根因分析模型"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "numpy>=1.26.0",
    "matplotlib>=3.8.0",
    "seaborn>=0.13.0",
    "jieba>=0.42.1",
    "wordcloud>=1.9.2",
    "scikit-learn>=1.4.0",
    "torch>=2.1.0",
    "transformers>=4.36.0",
    "datasets>=2.16.0",
    "accelerate>=0.25.0",
    "tokenizers>=0.15.0",
    "tqdm>=4.66.0",
    "joblib>=1.3.0",
    "plotly>=5.17.0",
    "kaleido>=0.2.1",
    "imbalanced-learn>=0.13.0",
]
