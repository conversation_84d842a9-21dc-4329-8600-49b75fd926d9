import pandas as pd

def deduplicate_and_save_excel(input_file, output_file, col1_name, col2_name):
    """
    读取Excel文件，获取指定两列数据，对这两列数据进行去重，
    并将去重后的数据写入一个新的Excel文件。

    Args:
        input_file (str): 输入Excel文件的路径。
        output_file (str): 输出Excel文件的路径。
        col1_name (str): 要处理的第一列的名称。
        col2_name (str): 要处理的第二列的名称。
    """
    try:
        # 1. 读取Excel文件
        df = pd.read_excel(input_file)
        print(f"成功读取文件：'{input_file}'")
        print(f"原始数据共 {len(df)} 行。")

        # 检查指定的列是否存在
        if col1_name not in df.columns:
            raise ValueError(f"输入文件中不存在列名：'{col1_name}'")
        if col2_name not in df.columns:
            raise ValueError(f"输入文件中不存在列名：'{col2_name}'")

        # 2. 获取这两列数据并创建新的DataFrame
        # 这里我们选择这两列以及它们的原始列名，保持数据结构
        df_selected = df[[col1_name, col2_name]]

        # 3. 对这两列数据的组合进行去重
        # subset 参数指定了根据哪些列进行去重
        df_deduplicated = df_selected.drop_duplicates(subset=[col1_name, col2_name])

        print(f"去重后数据共 {len(df_deduplicated)} 行。")
        print(f"共去除了 {len(df) - len(df_deduplicated)} 行重复数据。")

        # 4. 写入新的Excel文件
        df_deduplicated.to_excel(output_file, index=False) # index=False 不写入DataFrame的索引
        print(f"去重后的数据已成功写入到文件：'{output_file}'")

    except FileNotFoundError:
        print(f"错误：文件 '{input_file}' 未找到。请检查文件路径是否正确。")
    except ValueError as ve:
        print(f"数据错误：{ve}")
    except Exception as e:
        print(f"发生了一个未知错误：{e}")

# --- 配置你的文件和列名 ---
if __name__ == "__main__":
    # 确保 'your_input_file.xlsx' 存在于与脚本相同的目录下，或者提供完整路径
    input_excel_file = 'source.xlsx'
    output_excel_file = 'source_deduplicated.xlsx'

    # 请替换为你的Excel文件中实际的列名
    column_name_1 = '响应内容'
    column_name_2 = '报错原因'

    deduplicate_and_save_excel(
        input_excel_file,
        output_excel_file,
        column_name_1,
        column_name_2
    )